/**
 * Student Resume Integration Tests
 * Tests the complete student resume functionality including data mapping and rendering
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import useStudentStore from '../store/studentStore';

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: () => ({
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      },
      get: vi.fn(),
      put: vi.fn(),
      post: vi.fn()
    })
  }
}));

// Mock API response matching the structure you provided
const mockApiResponse = {
  success: true,
  data: {
    profile: {
      _id: "687b91d629bed4831477d6f1",
      Title: "<PERSON><PERSON><PERSON>",
      Email: "<EMAIL>",
      Template: "default",
      isPublished: false,
      PublishURL: null,
      PublicAccess: false,
      LastExported: null,
      PDFLink: null,
      Tags: [],
      Order: [],
      Profiles: [
        {
          Network: "GitHub",
          Username: "adarsh-kumar",
          ProfileLink: "https://github.com/adarsh-kumar",
          ProfileImage: "https://cdn.simpleicons.org/github",
          _id: "687b93da29bed4831477d752"
        }
      ],
      Experience: [
        {
          Company: "TechCorp",
          Position: "Full Stack Developer",
          StartDate: "2023-06-01T00:00:00.000Z",
          EndDate: "2025-01-15T00:00:00.000Z",
          Location: "Bengaluru",
          Website: "https://techcorp.com",
          Description: "Developed web applications using React and Node.js",
          _id: "687b942529bed4831477d757"
        }
      ],
      Education: [
        {
          Institution: "NIST",
          Degree: "B.Tech Computer Science",
          StartDate: "2020-08-01T00:00:00.000Z",
          EndDate: "2024-06-30T00:00:00.000Z",
          Location: "Berhampur",
          _id: "687b94aa29bed4831477d767"
        }
      ],
      Skills: [
        {
          Skill: "JavaScript",
          Proficiency: "Expert",
          Keywords: ["React", "Node.js"],
          Description: "5+ years experience",
          _id: "687b94ba29bed4831477d772"
        }
      ],
      Languages: [
        {
          Name: "English",
          Proficiency: "Fluent",
          Description: "Professional working proficiency",
          _id: "687b94d029bed4831477d78e"
        }
      ],
      Awards: [
        {
          Title: "Best Student Developer",
          Issuer: "NIST",
          Date: "2024-05-15T00:00:00.000Z",
          Description: "Outstanding performance",
          _id: "687b94f629bed4831477d7c7"
        }
      ],
      Certifications: [
        {
          Title: "AWS Certified Developer",
          Issuer: "Amazon",
          Date: "2024-03-10T00:00:00.000Z",
          Website: "https://aws.amazon.com/certification/",
          Description: "Associate level certification",
          _id: "687b949829bed4831477d75e"
        }
      ],
      Interests: [
        { Interest: "Open Source" },
        { Interest: "Machine Learning" }
      ],
      Publications: [
        {
          Title: "Modern Web Development",
          Publisher: "Tech Journal",
          Date: "2024-01-15T00:00:00.000Z",
          Website: "https://techjournal.com/article",
          Description: "Comprehensive guide",
          _id: "687b956929bed4831477d7f7"
        }
      ],
      Volunteering: [
        {
          Organization: "Code for Community",
          Position: "Technical Coordinator",
          StartDate: "2023-01-01T00:00:00.000Z",
          EndDate: "2024-12-31T00:00:00.000Z",
          Location: "Bengaluru",
          Description: "Leading technical initiatives",
          _id: "687b958b29bed4831477d812"
        }
      ],
      References: [
        {
          Name: "Dr. Rajesh Kumar",
          Position: "Professor",
          Company: "NIST",
          Email: "<EMAIL>",
          Phone: "+91-9876543210",
          _id: "687b95bf29bed4831477d82f"
        }
      ],
      Projects: [
        {
          Title: "E-Commerce Platform",
          Description: "Full-stack application with payment integration",
          Link: "https://github.com/user/ecommerce",
          Technologies: ["React", "Node.js", "MongoDB"],
          StartDate: "2023-09-01T00:00:00.000Z",
          EndDate: "2024-01-15T00:00:00.000Z",
          _id: "687b953929bed4831477d7de"
        }
      ],
      createdAt: "2025-01-19T12:38:46.538Z",
      updatedAt: "2025-01-19T12:56:05.586Z",
      __v: 0,
      Headline: "Full Stack Developer",
      Website: "https://adarsh-kumar.dev",
      Phone: "+91-9749038945",
      Location: "Bengaluru, Karnataka",
      summery: "Passionate developer with 3+ years experience",
      ProfileImagePublicId: "profile_pictures/user_1752929756304",
      ProfilePic: "https://res.cloudinary.com/dhqwyqekj/image/upload/v1752929765/profile_pictures/user_1752929756304.jpg"
    },
    completionStatus: 95
  }
};

describe('Student Resume Integration', () => {
  describe('Data Mapping and Structure', () => {
    it('should correctly map API response to profile structure', () => {
      const apiResponse = mockApiResponse.data;
      
      // Test basic info mapping
      expect(apiResponse.profile.Title).toBe('Adarsh Kumar');
      expect(apiResponse.profile.Email).toBe('<EMAIL>');
      expect(apiResponse.profile.Headline).toBe('Full Stack Developer');
      expect(apiResponse.profile.Phone).toBe('+91-9749038945');
      expect(apiResponse.profile.Location).toBe('Bengaluru, Karnataka');
      expect(apiResponse.profile.Website).toBe('https://adarsh-kumar.dev');
      expect(apiResponse.profile.ProfilePic).toContain('cloudinary.com');
      expect(apiResponse.profile.summery).toBe('Passionate developer with 3+ years experience');
    });

    it('should have properly structured arrays for resume sections', () => {
      const profile = mockApiResponse.data.profile;
      
      // Test array structures
      expect(Array.isArray(profile.Profiles)).toBe(true);
      expect(Array.isArray(profile.Experience)).toBe(true);
      expect(Array.isArray(profile.Education)).toBe(true);
      expect(Array.isArray(profile.Skills)).toBe(true);
      expect(Array.isArray(profile.Languages)).toBe(true);
      expect(Array.isArray(profile.Certifications)).toBe(true);
      expect(Array.isArray(profile.Awards)).toBe(true);
      expect(Array.isArray(profile.Projects)).toBe(true);
      expect(Array.isArray(profile.Publications)).toBe(true);
      expect(Array.isArray(profile.Volunteering)).toBe(true);
      expect(Array.isArray(profile.References)).toBe(true);
      expect(Array.isArray(profile.Interests)).toBe(true);
    });

    it('should have correct structure for Experience entries', () => {
      const experience = mockApiResponse.data.profile.Experience[0];
      
      expect(experience).toHaveProperty('Company');
      expect(experience).toHaveProperty('Position');
      expect(experience).toHaveProperty('StartDate');
      expect(experience).toHaveProperty('EndDate');
      expect(experience).toHaveProperty('Location');
      expect(experience).toHaveProperty('Website');
      expect(experience).toHaveProperty('Description');
      expect(experience).toHaveProperty('_id');
      
      expect(experience.Company).toBe('TechCorp');
      expect(experience.Position).toBe('Full Stack Developer');
    });

    it('should have correct structure for Education entries', () => {
      const education = mockApiResponse.data.profile.Education[0];
      
      expect(education).toHaveProperty('Institution');
      expect(education).toHaveProperty('Degree');
      expect(education).toHaveProperty('StartDate');
      expect(education).toHaveProperty('EndDate');
      expect(education).toHaveProperty('Location');
      expect(education).toHaveProperty('_id');
      
      expect(education.Institution).toBe('NIST');
      expect(education.Degree).toBe('B.Tech Computer Science');
    });

    it('should have correct structure for Skills entries', () => {
      const skill = mockApiResponse.data.profile.Skills[0];
      
      expect(skill).toHaveProperty('Skill');
      expect(skill).toHaveProperty('Proficiency');
      expect(skill).toHaveProperty('Keywords');
      expect(skill).toHaveProperty('Description');
      expect(skill).toHaveProperty('_id');
      
      expect(skill.Skill).toBe('JavaScript');
      expect(skill.Proficiency).toBe('Expert');
      expect(Array.isArray(skill.Keywords)).toBe(true);
      expect(skill.Keywords).toContain('React');
    });

    it('should have correct structure for Projects entries', () => {
      const project = mockApiResponse.data.profile.Projects[0];
      
      expect(project).toHaveProperty('Title');
      expect(project).toHaveProperty('Description');
      expect(project).toHaveProperty('Link');
      expect(project).toHaveProperty('Technologies');
      expect(project).toHaveProperty('StartDate');
      expect(project).toHaveProperty('EndDate');
      expect(project).toHaveProperty('_id');
      
      expect(project.Title).toBe('E-Commerce Platform');
      expect(Array.isArray(project.Technologies)).toBe(true);
      expect(project.Technologies).toContain('React');
    });
  });

  describe('Date Formatting', () => {
    it('should format dates correctly', () => {
      const formatDate = (date) => {
        if (!date) return 'Present';
        return new Date(date).toLocaleDateString();
      };

      const testDate = "2024-01-15T00:00:00.000Z";
      const formatted = formatDate(testDate);
      
      expect(formatted).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/); // MM/DD/YYYY or similar format
      expect(formatDate(null)).toBe('Present');
      expect(formatDate(undefined)).toBe('Present');
    });
  });

  describe('Resume Template Data Access', () => {
    it('should access nested profile data correctly', () => {
      const resumeData = mockApiResponse.data.profile;
      
      // Test accessing nested data as the template would
      const {
        Title,
        Email,
        Headline,
        Phone,
        Location,
        Website,
        ProfilePic,
        Profiles = [],
        Experience = [],
        Education = [],
        Skills = [],
        Languages = [],
        Certifications = [],
        Awards = [],
        Projects = [],
        Publications = [],
        Volunteering = [],
        References = [],
        Interests = [],
        summery = "",
      } = resumeData;

      expect(Title).toBe('Adarsh Kumar');
      expect(Email).toBe('<EMAIL>');
      expect(Profiles.length).toBeGreaterThan(0);
      expect(Experience.length).toBeGreaterThan(0);
      expect(Skills.length).toBeGreaterThan(0);
    });

    it('should handle empty arrays gracefully', () => {
      const emptyProfile = {
        ...mockApiResponse.data.profile,
        Profiles: [],
        Experience: [],
        Education: [],
        Skills: []
      };

      const {
        Profiles = [],
        Experience = [],
        Education = [],
        Skills = []
      } = emptyProfile;

      expect(Profiles.length).toBe(0);
      expect(Experience.length).toBe(0);
      expect(Education.length).toBe(0);
      expect(Skills.length).toBe(0);
    });
  });

  describe('Student Store Integration', () => {
    it('should store API data correctly in student store', () => {
      // This would test the actual store integration
      // For now, we test the structure that the store should handle
      expect(useStudentStore).toBeDefined();
      
      // Test that the store can handle the API response structure
      const apiResponse = mockApiResponse.data;
      expect(apiResponse.profile._id).toBeDefined();
      expect(apiResponse.completionStatus).toBe(95);
    });
  });

  describe('Resume Completeness', () => {
    it('should calculate completion status based on filled sections', () => {
      const profile = mockApiResponse.data.profile;
      
      const sections = [
        'Title', 'Email', 'Headline', 'Phone', 'Location', 
        'summery', 'Experience', 'Education', 'Skills'
      ];
      
      const filledSections = sections.filter(section => {
        const value = profile[section];
        if (Array.isArray(value)) return value.length > 0;
        return !!value && value !== '';
      });
      
      const completionPercentage = Math.round((filledSections.length / sections.length) * 100);
      expect(completionPercentage).toBeGreaterThan(80); // Should be well-filled profile
    });
  });
});
