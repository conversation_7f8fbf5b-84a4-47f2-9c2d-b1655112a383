import { create } from 'zustand';
import axios from 'axios';
import {
    API_BASE_URL,
    COMPANY_ENDPOINTS,
    QUESTION_ENDPOINTS,
    TEST_ENDPOINTS,
    CANDIDATE_ENDPOINTS,
    QUESTION_BUNDLE_ENDPOINTS
} from '../lib/constants';
import {
    mockCandidates,
    mockQuestionBundles
} from '../Components/company/data/mockQuestionData';
import {
    canModifyTest,
    canAddQuestionsToTest,
    canAssignCandidatesToTest,
    validateTestData,
    validateQuestionBundleData,
    getTestStatus
} from '../utils/testValidation';

const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const useCompanyStore = create((set) => ({
    loading: false,
    error: null,
    company: null,
    dashboard: null,
    jobs: [],
    jobDetails: null,
    questions: [],
    tests: [],
    testDetails: null,

    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),

    // Company Profile
    createCompanyProfile: async (data) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(COMPANY_ENDPOINTS.PROFILE, data);
            set({ company: res.data.company });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Profile creation failed' });
        } finally {
            set({ loading: false });
        }
    },

    getCompanyProfile: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.PROFILE);
            set({ company: res.data.company });
            return res.data.company;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch profile failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },
    updateCompanyProfile: async (data) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.put(COMPANY_ENDPOINTS.PROFILE, data);
            set({ company: res.data.company });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update failed' });
        } finally {
            set({ loading: false });
        }
    },
    // Jobs
    createJob: async (jobData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(COMPANY_ENDPOINTS.JOBS, jobData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job creation failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    getJobs: async () => {
        set({ loading: true });
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.JOBS);
            set({ jobs: res.data.jobs || [] });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch jobs failed' });
        } finally {
            set({ loading: false });
        }
    },

    getJobById: async (jobId) => {
        set({ loading: true });
        try {
            const res = await axiosInstance.get(`${COMPANY_ENDPOINTS.JOBS}/${jobId}`);
            set({ jobDetails: res.data.job });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job fetch failed' });
        } finally {
            set({ loading: false });
        }
    },

    updateJob: async (jobId, data) => {
        set({ loading: true });
        try {
            const res = await axiosInstance.put(`${COMPANY_ENDPOINTS.JOBS}/${jobId}`, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job update failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },
    updateJobStatus: async (jobId, isActive) => {
        try {
            const res = await axiosInstance.patch(COMPANY_ENDPOINTS.JOB_STATUS(jobId), { isActive });

            // Update the job in the local state
            set((state) => ({
                jobs: state.jobs.map(job =>
                    job._id === jobId ? { ...job, isActive } : job
                )
            }));

            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Status update failed' });
            return null;
        }
    },
    getJobApplications: async (jobId) => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.JOB_APPLICATIONS(jobId));
            return res.data.applications;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch applications failed' });
            return [];
        }
    },

    // Get applications with resumes for a specific job
    getJobApplicationsWithResumes: async (jobId, params = {}) => {
        try {
            const queryParams = new URLSearchParams(params);
            const url = `${COMPANY_ENDPOINTS.JOB_APPLICATIONS_WITH_RESUMES(jobId)}${queryParams.toString() ? `?${queryParams}` : ''}`;
            const res = await axiosInstance.get(url);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch applications with resumes failed' });
            return { success: false, data: [] };
        }
    },

    // Get all applications with resumes across jobs
    getAllApplicationsWithResumes: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams(params);
            const url = `${COMPANY_ENDPOINTS.APPLICATIONS_WITH_RESUMES}${queryParams.toString() ? `?${queryParams}` : ''}`;
            const res = await axiosInstance.get(url);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch all applications with resumes failed' });
            return { success: false, data: [] };
        }
    },

    // Get detailed candidate information for a specific job
    getCandidateDetails: async (jobId, candidateId) => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.CANDIDATE_DETAILS(jobId, candidateId));
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch candidate details failed' });
            return { success: false, data: null };
        }
    },

    // Get candidates analytics for a job
    getCandidatesAnalytics: async (jobId) => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.CANDIDATES_ANALYTICS(jobId));
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch candidates analytics failed' });
            return { success: false, data: null };
        }
    },
    // Dashboard
    getDashboardData: async () => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.DASHBOARD);
            set({ dashboard: res.data });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Dashboard fetch failed' });
        }
    },
    // Questions
    createQuestion: async (data) => {
        try {
            const res = await axiosInstance.post(QUESTION_ENDPOINTS.CREATE, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create question failed' });
            return null;
        }
    },
    getQuestions: async (params = {}) => {
        try {
            // Build query parameters
            const queryParams = new URLSearchParams();

            // Pagination parameters
            if (params.page) queryParams.append('page', params.page);
            if (params.limit) queryParams.append('limit', params.limit);

            // Filter parameters
            if (params.search) queryParams.append('search', params.search);
            if (params.category && params.category !== 'all') queryParams.append('category', params.category);
            if (params.difficulty && params.difficulty !== 'all') queryParams.append('difficulty', params.difficulty);
            if (params.questionType && params.questionType !== 'all') queryParams.append('questionType', params.questionType);

            // Sort parameters
            if (params.sortBy) queryParams.append('sortBy', params.sortBy);
            if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

            const url = `${QUESTION_ENDPOINTS.GET_ALL}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const res = await axiosInstance.get(url);

            const questions = res.data.questions || [];
            const pagination = res.data.pagination || {};
            const statistics = res.data.statistics || {};

            // Transform the API response to match our expected format
            const transformedQuestions = questions.map(q => ({
                _id: q._id,
                questionText: q.questionText,
                questionType: q.questionType, // Keep original field name
                type: q.questionType, // Map questionType to type for backward compatibility
                category: q.category,
                difficulty: q.difficulty,
                options: q.options || [],
                correctAnswer: q.correctAnswer,
                explanation: q.explanation,
                points: q.points || 1,
                tags: [q.category?.toLowerCase(), q.difficulty?.toLowerCase()].filter(Boolean), // Generate tags
                isActive: q.isActive,
                createdBy: q.createdBy,
                createdAt: q.createdAt,
                updatedAt: q.updatedAt
            }));

            // Update state with questions and pagination info
            set({
                questions: transformedQuestions,
                questionsPagination: pagination,
                questionsStatistics: statistics
            });

            return {
                questions: transformedQuestions,
                pagination,
                statistics
            };
        } catch (err) {
            console.error('Error fetching questions:', err);
            set({ error: err?.response?.data?.error || 'Fetch questions failed' });
            return {
                questions: [],
                pagination: { current: 1, pages: 1, total: 0 },
                statistics: { totalQuestions: 0, activeQuestions: 0 }
            };
        }
    },
    // Legacy method for backward compatibility - gets all questions without pagination
    getAllQuestions: async () => {
        try {
            const res = await axiosInstance.get(`${QUESTION_ENDPOINTS.GET_ALL}?limit=1000`);
            const questions = res.data.questions || [];

            const transformedQuestions = questions.map(q => ({
                _id: q._id,
                questionText: q.questionText,
                questionType: q.questionType,
                type: q.questionType,
                category: q.category,
                difficulty: q.difficulty,
                options: q.options || [],
                correctAnswer: q.correctAnswer,
                explanation: q.explanation,
                points: q.points || 1,
                tags: [q.category?.toLowerCase(), q.difficulty?.toLowerCase()].filter(Boolean),
                isActive: q.isActive,
                createdBy: q.createdBy,
                createdAt: q.createdAt,
                updatedAt: q.updatedAt
            }));

            return transformedQuestions;
        } catch (err) {
            console.error('Error fetching all questions:', err);
            return [];
        }
    },
    updateQuestion: async (id, data) => {
        try {
            const res = await axiosInstance.patch(QUESTION_ENDPOINTS.BY_ID(id), data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update question failed' });
            return null;
        }
    },
    deleteQuestion: async (id) => {
        try {
            await axiosInstance.delete(QUESTION_ENDPOINTS.BY_ID(id));
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete question failed' });
        }
    },
    uploadQuestionsFromExcel: async (file) => {
        try {
            const formData = new FormData();
            formData.append('excel', file);

            const res = await axiosInstance.post(QUESTION_ENDPOINTS.UPLOAD_EXCEL, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            return {
                success: true,
                data: res.data,
                message: res.data.message,
                summary: res.data.summary
            };
        } catch (err) {
            const errorData = err?.response?.data;
            set({ error: errorData?.error || 'Excel upload failed' });
            return {
                success: false,
                error: errorData?.error || 'Excel upload failed',
                details: errorData?.details,
                validationErrors: errorData?.validationErrors,
                expectedFormat: errorData?.expectedFormat
            };
        }
    },
    // Tests
    createTest: async (data) => {
        try {
            // Validate test data before sending
            const validation = validateTestData(data);
            if (!validation.isValid) {
                set({ error: validation.errors.join(', ') });
                return { success: false, errors: validation.errors };
            }

            const res = await axiosInstance.post(TEST_ENDPOINTS.CREATE, data);
            return res.data;
        } catch (err) {
            const errorMessage = err?.response?.data?.error || 'Create test failed';
            set({ error: errorMessage });
            return { success: false, error: errorMessage };
        }
    },
    getTests: async () => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.GET_ALL);
            set({ tests: res.data.tests });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch tests failed' });
        }
    },
    getTestDetails: async (id) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.BY_ID(id));
            set({ testDetails: res.data.test });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Test detail fetch failed' });
        }
    },
    assignTest: async (testId, assignData) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN(testId), assignData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Assign test failed' });
            return null;
        }
    },
    getTestResults: async (testId) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.RESULTS(testId));
            return res.data.results;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch results failed' });
            return [];
        }
    },

    getTestAnalytics: async (testId) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.ANALYTICS(testId));
            return res.data.analytics;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Analytics fetch failed' });
            return null;
        }
    },

    submitFeedback: async (testId, participantId, feedback) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.PARTICIPANT_FEEDBACK(testId, participantId), feedback);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Feedback submit failed' });
            return null;
        }
    },

    updateTest: async (testId, data) => {
        try {
            const res = await axiosInstance.put(TEST_ENDPOINTS.UPDATE(testId), data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update test failed' });
            return null;
        }
    },

    deleteTest: async (testId) => {
        try {
            await axiosInstance.delete(TEST_ENDPOINTS.DELETE(testId));
            // Remove from local state
            set((state) => ({
                tests: state.tests.filter(test => test._id !== testId)
            }));
            return { success: true };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete test failed' });
            return null;
        }
    },

    addQuestionsToTest: async (testId, questions) => {
        try {
            // Get test details for validation
            const testResponse = await axiosInstance.get(TEST_ENDPOINTS.BY_ID(testId));
            const test = testResponse.data.test;

            // Validate if questions can be added
            const questionIds = questions.map(q => q.questionId || q._id);
            const validation = canAddQuestionsToTest(test, questionIds);

            if (!validation.canAdd) {
                set({ error: validation.reason });
                return { success: false, error: validation.reason };
            }

            // Show warnings if any
            if (validation.warnings.length > 0) {
                console.warn('Question addition warnings:', validation.warnings);
            }

            // Transform questions array to questionIds array as expected by backend
            const points = questions.length > 0 ? questions[0].points : undefined;

            const payload = { questionIds };
            if (points !== undefined) {
                payload.points = points;
            }

            const res = await axiosInstance.post(TEST_ENDPOINTS.ADD_QUESTIONS(testId), payload);
            return { ...res.data, warnings: validation.warnings };
        } catch (err) {
            const errorMessage = err?.response?.data?.error || 'Add questions failed';
            set({ error: errorMessage });
            return { success: false, error: errorMessage };
        }
    },

    removeQuestionsFromTest: async (testId, questionIds) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.REMOVE_QUESTIONS(testId), { questionIds });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Remove questions failed' });
            return null;
        }
    },

    assignCandidatesToTest: async (testId, candidateIds) => {
        try {
            // Get test details for validation
            const testResponse = await axiosInstance.get(TEST_ENDPOINTS.BY_ID(testId));
            const test = testResponse.data.test;

            // Validate if candidates can be assigned
            const validation = canAssignCandidatesToTest(test, candidateIds);

            if (!validation.canAssign) {
                set({ error: validation.reason });
                return { success: false, error: validation.reason };
            }

            // Show warnings if any
            if (validation.warnings.length > 0) {
                console.warn('Candidate assignment warnings:', validation.warnings);
            }

            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN_CANDIDATES(testId), { candidateIds });
            return { ...res.data, warnings: validation.warnings };
        } catch (err) {
            const errorMessage = err?.response?.data?.error || 'Assign candidates failed';
            set({ error: errorMessage });
            return { success: false, error: errorMessage };
        }
    },

    // Candidates
    getCandidates: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams();

            // Only add non-empty parameters
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    queryParams.append(key, value);
                }
            });

            const queryString = queryParams.toString();
            const url = queryString ? `${TEST_ENDPOINTS.CANDIDATES}?${queryString}` : TEST_ENDPOINTS.CANDIDATES;

            const res = await axiosInstance.get(url);
            set({ candidates: res.data.candidates || [] });
            return res.data;
        } catch (err) {
            // Fallback to mock data if API fails
            console.warn('API failed, using mock data:', err);
            set({ candidates: mockCandidates });
            return { candidates: mockCandidates, pagination: { current: 1, pages: 1, total: mockCandidates.length } };
        }
    },

    searchCandidates: async (searchTerm, filters = {}) => {
        try {
            const params = new URLSearchParams();

            // Only add non-empty parameters
            if (searchTerm && searchTerm.trim()) {
                params.append('search', searchTerm.trim());
            }

            // Add filters only if they have values
            Object.entries(filters).forEach(([key, value]) => {
                if (value && value !== '' && value !== 'all') {
                    params.append(key, value);
                }
            });

            const queryString = params.toString();
            const url = queryString ? `${TEST_ENDPOINTS.CANDIDATES_SEARCH}?${queryString}` : TEST_ENDPOINTS.CANDIDATES_SEARCH;

            const res = await axiosInstance.get(url);
            return res.data;
        } catch (err) {
            // Fallback to mock data filtering if API fails
            console.warn('API failed, using mock data:', err);
            let filtered = mockCandidates;

            if (searchTerm) {
                filtered = filtered.filter(candidate =>
                    candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    candidate.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
                );
            }

            if (filters.experience) {
                filtered = filtered.filter(candidate => {
                    const exp = parseInt(candidate.experience);
                    switch (filters.experience) {
                        case '0-1': return exp <= 1;
                        case '1-3': return exp >= 1 && exp <= 3;
                        case '3-5': return exp >= 3 && exp <= 5;
                        case '5+': return exp >= 5;
                        default: return true;
                    }
                });
            }

            if (filters.skills) {
                filtered = filtered.filter(candidate =>
                    candidate.skills.some(skill =>
                        skill.toLowerCase().includes(filters.skills.toLowerCase())
                    )
                );
            }

            if (filters.location) {
                filtered = filtered.filter(candidate =>
                    candidate.location.toLowerCase().includes(filters.location.toLowerCase())
                );
            }

            if (filters.status) {
                filtered = filtered.filter(candidate => candidate.status === filters.status);
            }

            return { candidates: filtered, pagination: { current: 1, pages: 1, total: filtered.length } };
        }
    },

    getAvailableCandidatesForTest: async (testId) => {
        try {
            if (!testId) {
                throw new Error('Test ID is required');
            }

            const res = await axiosInstance.get(`${TEST_ENDPOINTS.CANDIDATES_AVAILABLE_FOR_TEST}?testId=${testId}`);
            return res.data;
        } catch (err) {
            // Fallback to mock data if API fails
            console.warn('API failed, using mock data:', err);
            const availableCandidates = mockCandidates.filter(candidate => candidate.status === 'available');
            return {
                candidates: availableCandidates,
                testName: 'Mock Test',
                associatedJobs: []
            };
        }
    },

    // Question Categories and Filtering
    getQuestionCategories: async () => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.QUESTION_CATEGORIES);
            set({ questionCategories: res.data.categories || [] });
            return res.data;
        } catch (err) {
            // Fallback to local categories if API fails
            console.warn('API failed, using local data:', err);
            const currentState = useCompanyStore.getState();

            // Get categories from existing questions
            if (currentState.questions && currentState.questions.length > 0) {
                const categories = [...new Set(currentState.questions.map(q => q.category).filter(Boolean))];
                const categoriesData = categories.map(cat => ({
                    category: cat,
                    count: currentState.questions.filter(q => q.category === cat).length
                }));
                set({ questionCategories: categoriesData });
                return { categories: categoriesData };
            }

            // If no questions loaded yet, fetch them first
            await useCompanyStore.getState().getQuestions();
            const updatedState = useCompanyStore.getState();
            const categories = [...new Set(updatedState.questions.map(q => q.category).filter(Boolean))];
            const categoriesData = categories.map(cat => ({
                category: cat,
                count: updatedState.questions.filter(q => q.category === cat).length
            }));
            set({ questionCategories: categoriesData });
            return { categories: categoriesData };
        }
    },

    getQuestionsByCategory: async (category) => {
        try {
            const res = await axiosInstance.get(`${TEST_ENDPOINTS.QUESTIONS_BY_CATEGORY}?category=${category}`);
            return res.data;
        } catch (err) {
            // Fallback to local filtering if API fails
            console.warn('API failed, using local data:', err);
            const state = useCompanyStore.getState();

            // If questions not loaded, fetch them first
            if (!state.questions || state.questions.length === 0) {
                await useCompanyStore.getState().getQuestions();
            }

            // Filter questions by category
            const updatedState = useCompanyStore.getState();
            const filtered = updatedState.questions.filter(q => q.category === category);
            return {
                success: true,
                category,
                count: filtered.length,
                questions: filtered
            };
        }
    },

    filterQuestions: async (filters) => {
        try {
            const params = new URLSearchParams();
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    params.append(key, filters[key]);
                }
            });

            const res = await axiosInstance.get(`${TEST_ENDPOINTS.FILTER_QUESTIONS}?${params}`);
            return res.data;
        } catch (err) {
            // Fallback to local filtering if API fails
            console.warn('API failed, using local data:', err);
            const state = useCompanyStore.getState();

            // If questions not loaded, fetch them first
            if (!state.questions || state.questions.length === 0) {
                await useCompanyStore.getState().getQuestions();
            }

            const updatedState = useCompanyStore.getState();
            let filtered = updatedState.questions;

            if (filters.searchTerm) {
                filtered = filtered.filter(q =>
                    q.questionText.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
                    q.category?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
                    q.tags?.some(tag => tag.toLowerCase().includes(filters.searchTerm.toLowerCase()))
                );
            }

            if (filters.category) {
                filtered = filtered.filter(q => q.category === filters.category);
            }

            if (filters.difficulty) {
                filtered = filtered.filter(q => q.difficulty === filters.difficulty);
            }

            if (filters.type) {
                filtered = filtered.filter(q => q.type === filters.type);
            }

            // Simulate pagination
            const page = parseInt(filters.page) || 1;
            const limit = parseInt(filters.limit) || 10;
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedQuestions = filtered.slice(startIndex, endIndex);

            return {
                success: true,
                questions: paginatedQuestions,
                pagination: {
                    current: page,
                    pages: Math.ceil(filtered.length / limit),
                    total: filtered.length,
                    limit: limit
                }
            };
        }
    },

    // Question Bundles
    createQuestionBundle: async (bundleData) => {
        try {
            // Validate bundle data
            const validation = validateQuestionBundleData(bundleData);
            if (!validation.isValid) {
                set({ error: validation.errors.join(', ') });
                return { success: false, errors: validation.errors };
            }

            const res = await axiosInstance.post(QUESTION_BUNDLE_ENDPOINTS.CREATE, bundleData);

            // Update local state
            set((state) => ({
                questionBundles: [...(state.questionBundles || []), res.data.bundle]
            }));

            return res.data;
        } catch (err) {
            // Fallback to mock creation if API fails
            console.warn('API failed, using mock data:', err);

            // Still validate even for mock data
            const validation = validateQuestionBundleData(bundleData);
            if (!validation.isValid) {
                set({ error: validation.errors.join(', ') });
                return { success: false, errors: validation.errors };
            }

            const newBundle = {
                _id: 'b' + Date.now(),
                ...bundleData,
                createdAt: new Date()
            };

            set((state) => ({
                questionBundles: [...(state.questionBundles || []), newBundle]
            }));

            return { success: true, message: 'Question bundle created successfully', bundle: newBundle };
        }
    },

    getQuestionBundles: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams(params);
            const res = await axiosInstance.get(`${QUESTION_BUNDLE_ENDPOINTS.GET_ALL}?${queryParams}`);
            set({ questionBundles: res.data.bundles || [] });
            return res.data;
        } catch (err) {
            // Fallback to mock data if API fails
            console.warn('API failed, using mock data:', err);
            set({ questionBundles: mockQuestionBundles });
            return {
                success: true,
                bundles: mockQuestionBundles,
                pagination: { current: 1, pages: 1, total: mockQuestionBundles.length }
            };
        }
    },

    getQuestionBundleById: async (bundleId) => {
        try {
            const res = await axiosInstance.get(QUESTION_BUNDLE_ENDPOINTS.BY_ID(bundleId));
            return res.data;
        } catch (err) {
            // Fallback to mock data if API fails
            console.warn('API failed, using mock data:', err);
            const bundle = mockQuestionBundles.find(b => b._id === bundleId);
            return bundle ? { success: true, bundle } : { success: false, error: 'Bundle not found' };
        }
    },

    updateQuestionBundle: async (bundleId, bundleData) => {
        try {
            const res = await axiosInstance.put(QUESTION_BUNDLE_ENDPOINTS.UPDATE(bundleId), bundleData);

            // Update local state
            set((state) => ({
                questionBundles: (state.questionBundles || []).map(bundle =>
                    bundle._id === bundleId ? { ...bundle, ...bundleData } : bundle
                )
            }));

            return res.data;
        } catch (err) {
            // Fallback to mock update if API fails
            console.warn('API failed, using mock data:', err);
            set((state) => ({
                questionBundles: (state.questionBundles || []).map(bundle =>
                    bundle._id === bundleId ? { ...bundle, ...bundleData } : bundle
                )
            }));

            return { success: true, message: 'Question bundle updated successfully' };
        }
    },

    deleteQuestionBundle: async (bundleId) => {
        try {
            await axiosInstance.delete(QUESTION_BUNDLE_ENDPOINTS.DELETE(bundleId));

            // Update local state
            set((state) => ({
                questionBundles: (state.questionBundles || []).filter(bundle => bundle._id !== bundleId)
            }));

            return { success: true, message: 'Question bundle deleted successfully' };
        } catch (err) {
            // Fallback to mock deletion if API fails
            console.warn('API failed, using mock data:', err);
            set((state) => ({
                questionBundles: (state.questionBundles || []).filter(bundle => bundle._id !== bundleId)
            }));

            return { success: true, message: 'Question bundle deleted successfully' };
        }
    },

    // Additional API functions for comprehensive test management

    // Direct candidate assignment to test
    assignCandidatesToTestDirect: async (testId, candidateIds) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN_CANDIDATES(testId), { candidateIds });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Direct candidate assignment failed' });
            return null;
        }
    },

    // Remove questions from test (DELETE method)
    removeQuestionsFromTestDelete: async (testId, questionIds) => {
        try {
            const res = await axiosInstance.delete(TEST_ENDPOINTS.REMOVE_QUESTIONS(testId), {
                data: { questionIds }
            });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Remove questions failed' });
            return null;
        }
    },

    // Get candidates by job ID
    getCandidatesByJob: async (jobId, params = {}) => {
        try {
            const queryParams = new URLSearchParams({ jobId, ...params });
            const res = await axiosInstance.get(`${TEST_ENDPOINTS.CANDIDATES}?${queryParams}`);
            return res.data;
        } catch (err) {
            // Fallback to mock data filtering
            console.warn('API failed, using mock data:', err);
            const filtered = mockCandidates.filter(candidate =>
                candidate.appliedJobs && candidate.appliedJobs.includes(jobId)
            );
            return {
                success: true,
                candidates: filtered,
                pagination: { current: 1, pages: 1, total: filtered.length }
            };
        }
    },

    // Bulk Operations

    // Bulk candidate assignment to multiple tests
    bulkAssignCandidatesToTests: async (testIds, candidateIds) => {
        try {
            const results = [];
            for (const testId of testIds) {
                const result = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN_CANDIDATES(testId), { candidateIds });
                results.push({ testId, result: result.data, success: true });
            }
            return { success: true, results, totalTests: testIds.length, totalCandidates: candidateIds.length };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Bulk candidate assignment failed' });
            return null;
        }
    },

    // Bulk question assignment to multiple tests
    bulkAddQuestionsToTests: async (testIds, questions) => {
        try {
            const questionIds = questions.map(q => q.questionId || q._id);
            const points = questions.length > 0 ? questions[0].points : undefined;

            const payload = { questionIds };
            if (points !== undefined) {
                payload.points = points;
            }

            const results = [];
            for (const testId of testIds) {
                const result = await axiosInstance.post(TEST_ENDPOINTS.ADD_QUESTIONS(testId), payload);
                results.push({ testId, result: result.data, success: true });
            }
            return { success: true, results, totalTests: testIds.length, totalQuestions: questionIds.length };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Bulk question assignment failed' });
            return null;
        }
    },

    // Bulk create question bundles from categories
    bulkCreateQuestionBundlesByCategory: async (categories, bundlePrefix = 'Bundle') => {
        try {
            const results = [];

            for (const category of categories) {
                // Get questions for this category
                const categoryQuestions = await axiosInstance.get(`${TEST_ENDPOINTS.QUESTIONS_BY_CATEGORY}?category=${category}`);
                const questions = categoryQuestions.data.questions || [];

                if (questions.length > 0) {
                    const bundleData = {
                        bundleName: `${bundlePrefix} - ${category}`,
                        description: `Auto-generated bundle for ${category} questions`,
                        category: category,
                        difficulty: 'Medium', // Default difficulty
                        questionIds: questions.map(q => q._id),
                        tags: [category.toLowerCase(), 'auto-generated']
                    };

                    const result = await axiosInstance.post(QUESTION_BUNDLE_ENDPOINTS.CREATE, bundleData);
                    results.push({ category, bundle: result.data.bundle, success: true });
                }
            }

            // Update local state
            const newBundles = results.map(r => r.bundle).filter(Boolean);
            set((state) => ({
                questionBundles: [...(state.questionBundles || []), ...newBundles]
            }));

            return { success: true, results, totalBundles: results.length };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Bulk bundle creation failed' });
            return null;
        }
    },

    // Bulk delete question bundles
    bulkDeleteQuestionBundles: async (bundleIds) => {
        try {
            const results = [];

            for (const bundleId of bundleIds) {
                await axiosInstance.delete(QUESTION_BUNDLE_ENDPOINTS.DELETE(bundleId));
                results.push({ bundleId, success: true });
            }

            // Update local state
            set((state) => ({
                questionBundles: (state.questionBundles || []).filter(bundle => !bundleIds.includes(bundle._id))
            }));

            return { success: true, results, totalDeleted: bundleIds.length };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Bulk bundle deletion failed' });
            return null;
        }
    },

    // Bulk update test status
    bulkUpdateTestStatus: async (testIds, status) => {
        try {
            const results = [];

            for (const testId of testIds) {
                const result = await axiosInstance.put(TEST_ENDPOINTS.UPDATE(testId), { status });
                results.push({ testId, result: result.data, success: true });
            }

            return { success: true, results, totalTests: testIds.length };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Bulk test status update failed' });
            return null;
        }
    },

    // Utility functions

    // Get test status with validation info
    getTestStatusInfo: (test) => {
        return getTestStatus(test);
    },

    // Check if test can be modified
    checkTestModification: (test) => {
        return canModifyTest(test);
    },

    // Validate test data
    validateTest: (testData) => {
        return validateTestData(testData);
    },

    // Validate question bundle data
    validateBundle: (bundleData) => {
        return validateQuestionBundleData(bundleData);
    },

    clearError: () => set({ error: null }),
}));

export default useCompanyStore;
